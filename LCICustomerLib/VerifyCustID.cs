// Convert customerID to lowercase for case-insensitive comparison
var customer = Db.Customer.FirstOrDefault(cust => cust.CustID.ToLower() == inputCustID.ToLower());

if (customer == null)
{
    outputCustID = "NONE";
    return;
}
else{
    outputCustID = customer.CustID.ToString();
}// Convert customerID to lowercase for case-insensitive comparison
var customer = Db.Customer.FirstOrDefault(cust => cust.CustID.ToLower() == inputCustID.ToLower());

if (customer == null)
{
    outputCustID = "NONE";
    return;
}
else{
    outputCustID = customer.CustID.ToString();
}