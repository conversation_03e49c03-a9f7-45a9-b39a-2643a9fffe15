/*
 * GetCustomerWithShipTo - Epicor Function
 *
 * DESCRIPTION:
 * This function searches for customers by name (partial or complete match) and returns
 * comprehensive customer information along with their primary ShipTo data in a structured DataSet.
 *
 * INPUT PARAMETERS:
 * - inputCustomerName (string): Customer name to search for. Can be:
 *   • Complete name: "ABC Company Inc"
 *   • Partial name: "ABC" (will find "ABC Company", "ABC Industries", etc.)
 *   • Case-insensitive: "abc company" will match "ABC Company"
 *
 * OUTPUT PARAMETERS:
 * - outputDataSet (System.Data.DataSet): Contains two DataTables with customer and ShipTo information
 *
 * DATASET STRUCTURE:
 *
 * Table 1: "Customer" - Contains basic customer information
 * ┌─────────────┬──────────┬─────────────────────────────────────────┐
 * │ Column Name │ Data Type│ Description                             │
 * ├─────────────┼──────────┼─────────────────────────────────────────┤
 * │ CustID      │ string   │ Customer ID (primary identifier)        │
 * │ Name        │ string   │ Customer company name                   │
 * │ CustNum     │ int      │ Customer number (internal ID)           │
 * │ Address1    │ string   │ Primary address line                    │
 * │ Address2    │ string   │ Secondary address line                  │
 * │ Address3    │ string   │ Third address line                      │
 * │ City        │ string   │ City                                    │
 * │ State       │ string   │ State/Province                          │
 * │ Zip         │ string   │ ZIP/Postal code                         │
 * │ Country     │ string   │ Country                                 │
 * │ PhoneNum    │ string   │ Primary phone number                    │
 * │ FaxNum      │ string   │ Fax number                              │
 * │ EMailAddress│ string   │ Email address                           │
 * │ Active      │ bool     │ Customer active status (true/false)     │
 * └─────────────┴──────────┴─────────────────────────────────────────┘
 *
 * Table 2: "ShipTo" - Contains primary ShipTo information for each customer
 * ┌─────────────┬──────────┬─────────────────────────────────────────┐
 * │ Column Name │ Data Type│ Description                             │
 * ├─────────────┼──────────┼─────────────────────────────────────────┤
 * │ CustID      │ string   │ Customer ID (links to Customer table)   │
 * │ CustNum     │ int      │ Customer number (links to Customer)     │
 * │ ShipToNum   │ string   │ ShipTo identifier                       │
 * │ Name        │ string   │ ShipTo location name                    │
 * │ Address1    │ string   │ ShipTo address line 1                   │
 * │ Address2    │ string   │ ShipTo address line 2                   │
 * │ Address3    │ string   │ ShipTo address line 3                   │
 * │ City        │ string   │ ShipTo city                             │
 * │ State       │ string   │ ShipTo state/province                   │
 * │ Zip         │ string   │ ShipTo ZIP/postal code                  │
 * │ Country     │ string   │ ShipTo country                          │
 * │ PhoneNum    │ string   │ ShipTo phone number                     │
 * │ FaxNum      │ string   │ ShipTo fax number                       │
 * │ EMailAddress│ string   │ ShipTo email address                    │
 * │ Primary     │ bool     │ Is this the primary ShipTo? (true/false)│
 * └─────────────┴──────────┴─────────────────────────────────────────┘
 *
 * USAGE EXAMPLES:
 *
 * Example 1 - Search for exact customer name:
 * Input: inputCustomerName = "Acme Corporation"
 * Result: Returns one customer record with matching ShipTo if found
 *
 * Example 2 - Search for partial customer name:
 * Input: inputCustomerName = "Acme"
 * Result: Returns all customers with "Acme" in their name:
 *         - "Acme Corporation"
 *         - "Acme Industries"
 *         - "Super Acme Company"
 *
 * Example 3 - Case-insensitive search:
 * Input: inputCustomerName = "acme corp"
 * Result: Finds "Acme Corporation", "ACME CORP", etc.
 *
 * BEHAVIOR NOTES:
 * • If no customers match the search criteria, returns empty DataSet with table structure intact
 * • For each customer, attempts to find PRIMARY ShipTo first
 * • If no primary ShipTo exists, returns the first available ShipTo for that customer
 * • If customer has no ShipTo records, only customer data is returned
 * • All string fields are null-safe (empty string returned if null)
 * • Multiple customers can be returned in a single result set
 *
 * EPICOR FUNCTION REQUIREMENTS:
 * • No using statements (Epicor constraint)
 * • Void return type with output parameter
 * • No member declarations
 * • Uses Db.Customer and Db.ShipTo for data access
 *
 * INTEGRATION:
 * This function can be called from Epicor BPMs, Method Directives, or other custom functions
 * to retrieve comprehensive customer and shipping information for business logic processing.
 */

// Create DataSet to hold customer and shipTo information
var dataSet = new System.Data.DataSet("CustomerData");

// Create Customer DataTable
var customerTable = new System.Data.DataTable("Customer");
customerTable.Columns.Add("CustID", typeof(string));
customerTable.Columns.Add("Name", typeof(string));
customerTable.Columns.Add("CustNum", typeof(int));
customerTable.Columns.Add("Address1", typeof(string));
customerTable.Columns.Add("Address2", typeof(string));
customerTable.Columns.Add("Address3", typeof(string));
customerTable.Columns.Add("City", typeof(string));
customerTable.Columns.Add("State", typeof(string));
customerTable.Columns.Add("Zip", typeof(string));
customerTable.Columns.Add("Country", typeof(string));
customerTable.Columns.Add("PhoneNum", typeof(string));
customerTable.Columns.Add("FaxNum", typeof(string));
customerTable.Columns.Add("EMailAddress", typeof(string));
customerTable.Columns.Add("Active", typeof(bool));

// Create ShipTo DataTable
var shipToTable = new System.Data.DataTable("ShipTo");
shipToTable.Columns.Add("CustID", typeof(string));
shipToTable.Columns.Add("CustNum", typeof(int));
shipToTable.Columns.Add("ShipToNum", typeof(string));
shipToTable.Columns.Add("Name", typeof(string));
shipToTable.Columns.Add("Address1", typeof(string));
shipToTable.Columns.Add("Address2", typeof(string));
shipToTable.Columns.Add("Address3", typeof(string));
shipToTable.Columns.Add("City", typeof(string));
shipToTable.Columns.Add("State", typeof(string));
shipToTable.Columns.Add("Zip", typeof(string));
shipToTable.Columns.Add("Country", typeof(string));
shipToTable.Columns.Add("PhoneNum", typeof(string));
shipToTable.Columns.Add("FaxNum", typeof(string));
shipToTable.Columns.Add("EMailAddress", typeof(string));
shipToTable.Columns.Add("Primary", typeof(bool));

// Add tables to dataset
dataSet.Tables.Add(customerTable);
dataSet.Tables.Add(shipToTable);

// Search for customers with names containing the input (case-insensitive)
var customers = Db.Customer.Where(cust => cust.Name.ToLower().Contains(inputCustomerName.ToLower())).ToList();

if (customers.Count == 0)
{
    // Return empty dataset if no customers found
    outputDataSet = dataSet;
    return;
}

// Process each matching customer
foreach (var customer in customers)
{
    // Add customer row
    var customerRow = customerTable.NewRow();
    customerRow["CustID"] = customer.CustID ?? "";
    customerRow["Name"] = customer.Name ?? "";
    customerRow["CustNum"] = customer.CustNum;
    customerRow["Address1"] = customer.Address1 ?? "";
    customerRow["Address2"] = customer.Address2 ?? "";
    customerRow["Address3"] = customer.Address3 ?? "";
    customerRow["City"] = customer.City ?? "";
    customerRow["State"] = customer.State ?? "";
    customerRow["Zip"] = customer.Zip ?? "";
    customerRow["Country"] = customer.Country ?? "";
    customerRow["PhoneNum"] = customer.PhoneNum ?? "";
    customerRow["FaxNum"] = customer.FaxNum ?? "";
    customerRow["EMailAddress"] = customer.EMailAddress ?? "";
    customerRow["Active"] = !customer.Inactive; // Inactive field is inverted
    
    customerTable.Rows.Add(customerRow);
    
    // Find primary ShipTo for this customer
    var primaryShipTo = Db.ShipTo.FirstOrDefault(st => st.CustNum == customer.CustNum && st.ShipToNum == "");
    
    if (primaryShipTo != null)
    {
        // Add primary ShipTo row
        var shipToRow = shipToTable.NewRow();
        shipToRow["CustID"] = customer.CustID ?? "";
        shipToRow["CustNum"] = customer.CustNum;
        shipToRow["ShipToNum"] = primaryShipTo.ShipToNum ?? "";
        shipToRow["Name"] = primaryShipTo.Name ?? "";
        shipToRow["Address1"] = primaryShipTo.Address1 ?? "";
        shipToRow["Address2"] = primaryShipTo.Address2 ?? "";
        shipToRow["Address3"] = primaryShipTo.Address3 ?? "";
        shipToRow["City"] = primaryShipTo.City ?? "";
        shipToRow["State"] = primaryShipTo.State ?? "";
        shipToRow["Zip"] = primaryShipTo.ZIP ?? "";
        shipToRow["Country"] = primaryShipTo.Country ?? "";
        shipToRow["PhoneNum"] = primaryShipTo.PhoneNum ?? "";
        shipToRow["FaxNum"] = primaryShipTo.FaxNum ?? "";
        shipToRow["EMailAddress"] = primaryShipTo.EMailAddress ?? "";
        shipToRow["Primary"] = true;
        
        shipToTable.Rows.Add(shipToRow);
    }
    else
    {
        // If no primary ShipTo found, try to get the first ShipTo for this customer
        var firstShipTo = Db.ShipTo.FirstOrDefault(st => st.CustNum == customer.CustNum);
        
        if (firstShipTo != null)
        {
            // Add first available ShipTo row
            var shipToRow = shipToTable.NewRow();
            shipToRow["CustID"] = customer.CustID ?? "";
            shipToRow["CustNum"] = customer.CustNum;
            shipToRow["ShipToNum"] = firstShipTo.ShipToNum ?? "";
            shipToRow["Name"] = firstShipTo.Name ?? "";
            shipToRow["Address1"] = firstShipTo.Address1 ?? "";
            shipToRow["Address2"] = firstShipTo.Address2 ?? "";
            shipToRow["Address3"] = firstShipTo.Address3 ?? "";
            shipToRow["City"] = firstShipTo.City ?? "";
            shipToRow["State"] = firstShipTo.State ?? "";
            shipToRow["Zip"] = firstShipTo.ZIP ?? "";
            shipToRow["Country"] = firstShipTo.Country ?? "";
            shipToRow["PhoneNum"] = firstShipTo.PhoneNum ?? "";
            shipToRow["FaxNum"] = firstShipTo.FaxNum ?? "";
            shipToRow["EMailAddress"] = firstShipTo.EMailAddress ?? "";
            shipToRow["Primary"] = firstShipTo.ShipToNum == "";
            
            shipToTable.Rows.Add(shipToRow);
        }
    }
}

// Return the populated dataset
outputDataSet = dataSet;
